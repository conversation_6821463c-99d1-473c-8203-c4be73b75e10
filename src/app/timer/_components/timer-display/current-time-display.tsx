'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Clock } from 'lucide-react';
import { usePomodoroStore } from '@/lib/pomodoro-store';

export function StandaloneCurrentTimeDisplay() {
  const [currentTime, setCurrentTime] = useState('');
  const [showControls, setShowControls] = useState(true);
  const showCurrentTime = usePomodoroStore((state) => state.showCurrentTime);

  // Update current time every second
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
      setCurrentTime(timeString);
    };

    // Update immediately
    updateTime();

    // Set up interval to update every second
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  // Listen for navigation controls visibility changes
  useEffect(() => {
    const handleNavigationToggle = (event: CustomEvent<{ visible: boolean }>) => {
      setShowControls(event.detail.visible);
    };

    window.addEventListener('navigation-controls-toggle', handleNavigationToggle as EventListener);

    return () => {
      window.removeEventListener('navigation-controls-toggle', handleNavigationToggle as EventListener);
    };
  }, []);

  // Don't render if the setting is disabled
  if (!showCurrentTime) {
    return null;
  }

  return (
    <div
      className={cn(
        "fixed z-40 transition-all duration-300 ease-out",
        "flex items-center gap-1.5 sm:gap-2 bg-background/20 backdrop-blur-md border border-white/20 rounded-lg",
        "px-2 sm:px-2.5 py-1 sm:py-1.5",
        "text-white text-xs font-mono font-medium tabular-nums",
        showControls ? "opacity-100" : "opacity-0 pointer-events-none"
      )}
      style={{
        top: '3.5rem', // Position below navigation controls
        right: '0.75rem', // Align with navigation controls
        animationDelay: '150ms'
      }}
      role="status"
      aria-label={`Current time: ${currentTime}`}
    >
      <Clock className="h-3 w-3 text-white/80 flex-shrink-0" />
      <span className="text-white/90 text-xs">
        {currentTime}
      </span>
    </div>
  );
}

// Legacy component for backward compatibility (used within timer display)
interface CurrentTimeDisplayProps {
  showControls: boolean;
  timerColor: any;
  timerSize?: {
    timeScale?: number;
  } | null;
}

export function CurrentTimeDisplay({
  showControls,
  timerColor,
  timerSize
}: CurrentTimeDisplayProps) {
  const [currentTime, setCurrentTime] = useState('');

  // Update current time every second
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
      setCurrentTime(timeString);
    };

    // Update immediately
    updateTime();

    // Set up interval to update every second
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  // Calculate responsive sizing based on timer scale
  const getResponsiveStyles = () => {
    const baseScale = timerSize?.timeScale || 1;

    if (baseScale > 1.3) {
      return {
        fontSize: `${Math.max(0.7, Math.min(0.9, 0.7 + (baseScale - 1.3) * 0.2))}rem`,
        iconSize: Math.max(12, Math.min(16, 12 + (baseScale - 1.3) * 4)),
        padding: `${Math.max(0.3, Math.min(0.6, 0.3 + (baseScale - 1.3) * 0.3))}rem`,
        gap: `${Math.max(0.3, Math.min(0.5, 0.3 + (baseScale - 1.3) * 0.2))}rem`
      };
    }

    return {
      fontSize: '0.7rem',
      iconSize: 12,
      padding: '0.3rem',
      gap: '0.3rem'
    };
  };

  const styles = getResponsiveStyles();

  return (
    <div
      className={cn(
        "flex items-center justify-center transition-all duration-300 ease-out",
        "bg-black/20 backdrop-blur-sm rounded-lg border border-white/10",
        "timer-control-fade",
        showControls ? "show opacity-100" : "hide opacity-0 pointer-events-none"
      )}
      style={{
        padding: styles.padding,
        gap: styles.gap,
        fontSize: styles.fontSize,
        marginTop: showControls ? '0.5rem' : '0',
        animationDelay: '120ms'
      }}
      role="status"
      aria-label={`Current time: ${currentTime}`}
    >
      <Clock
        className="flex-shrink-0 transition-colors duration-200 text-white/80"
        style={{
          width: `${styles.iconSize}px`,
          height: `${styles.iconSize}px`
        }}
      />
      <span
        className="font-mono font-medium tabular-nums transition-colors duration-200 text-white/90"
        style={{ fontSize: styles.fontSize }}
      >
        {currentTime}
      </span>
    </div>
  );
}
